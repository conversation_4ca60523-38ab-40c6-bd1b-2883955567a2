{% extends "base.html" %} {% block content %}
<form
  id="externalloadsform"
  method="POST"
  action="javascript:void(0);"
  onsubmit="return false;"
>
  <div class="container">
    <h3>External Loads Input</h3>

    <div
      class="button-group"
      style="
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-bottom: 20px;
      "
    >
      <button
        type="button"
        class="tab-button active"
        onclick="showSection('dead_loads')"
      >
        Dead Loads
      </button>
      <button
        type="button"
        class="tab-button"
        onclick="showSection('live_loads')"
      >
        Live Loads
      </button>
      <button
        type="button"
        class="tab-button"
        onclick="showSection('strip_loads')"
      >
        Strip Loads
      </button>
      <button
        type="button"
        class="tab-button"
        onclick="showSection('earthquake_loads')"
      >
        Earthquake Loads
      </button>
      <button
        type="button"
        class="tab-button"
        onclick="showSection('impact_loads')"
      >
        Impact Loads
      </button>
    </div>

    <div class="load-sections">
      <div id="dead_loads" class="load-input-section">
        <h3>Dead Loads</h3>
        {% for i in range(1, 4) %}
        <div class="input-group">
          <label for="dead_load{{ i }}">Dead Load {{ i }} (kN/m²):</label>
          <input
            type="number"
            id="dead_load{{ i }}"
            name="dead_load{{ i }}"
            value="{{ dead_loads[i-1] }}"
            step="0.01"
            min="0"
          />
          <span class="error-message" id="dead_load{{ i }}-error"></span>
        </div>
        {% endfor %}
      </div>

      <div id="live_loads" class="load-input-section">
        <h3>Live Loads</h3>
        {% for i in range(1, 4) %}
        <div class="input-group">
          <label for="live_load{{ i }}">Live Load {{ i }} (kN/m²):</label>
          <input
            type="number"
            id="live_load{{ i }}"
            name="live_load{{ i }}"
            value="{{ live_loads[i-1] }}"
            step="0.01"
            min="0"
          />
          <span class="error-message" id="live_load{{ i }}-error"></span>
        </div>
        {% endfor %}
      </div>

      <div id="strip_loads" class="load-input-section">
        <h3>Strip Loads</h3>
        <div class="strip-load-container">
          {% for i in range(1, 4) %}
          <div class="strip-load-box">
            <h4>Strip Load {{ i }}</h4>
            <div class="input-group">
              <label for="vertical_strip_load{{ i }}"
                >Vertical Load (kN/m):</label
              >
              <input
                type="number"
                id="vertical_strip_load{{ i }}"
                name="vertical_strip_load{{ i }}"
                value="{{ vertical_strip_load[i-1] }}"
                step="0.01"
                min="0"
              />
              <span
                class="error-message"
                id="vertical_strip_load{{ i }}-error"
              ></span>
            </div>
            <div class="input-group">
              <label for="horizontal_strip_load{{ i }}"
                >Horizontal Load (kN/m):</label
              >
              <input
                type="number"
                id="horizontal_strip_load{{ i }}"
                name="horizontal_strip_load{{ i }}"
                value="{{ horizontal_strip_load[i-1] }}"
                step="0.01"
              />
              <span
                class="error-message"
                id="horizontal_strip_load{{ i }}-error"
              ></span>
            </div>
            <div class="input-group">
              <label for="strip_load_width{{ i }}">Width (m):</label>
              <input
                type="number"
                id="strip_load_width{{ i }}"
                name="strip_load_width{{ i }}"
                value="{{ strip_load_width[i-1] }}"
                step="0.01"
                min="0"
              />
              <span
                class="error-message"
                id="strip_load_width{{ i }}-error"
              ></span>
            </div>
            <div class="input-group">
              <label for="strip_load_distance{{ i }}"
                >Distance from Wall (m):</label
              >
              <input
                type="number"
                id="strip_load_distance{{ i }}"
                name="strip_load_distance{{ i }}"
                value="{{ strip_load_distance[i-1] }}"
                step="0.01"
                min="0"
              />
              <span
                class="error-message"
                id="strip_load_distance{{ i }}-error"
              ></span>
            </div>
          </div>
          {% endfor %}
        </div>
      </div>

      <div id="earthquake_loads" class="load-input-section">
        <h3>Earthquake Loads</h3>
        <div class="input-group">
          <label for="earthquake_acceleration">Acceleration (g):</label>
          <input
            type="number"
            id="earthquake_acceleration"
            name="earthquake_acceleration"
            value="{{ earthquake_acceleration }}"
            step="0.01"
            min="0"
            max="2"
          />
          <span class="error-message" id="earthquake_acceleration-error"></span>
        </div>
        <div class="input-group">
          <input
            type="checkbox"
            id="use_direct_kh"
            name="use_direct_kh"
            onclick="toggleKhInput()"
            {%
            if
            use_direct_kh
            %}checked{%
            endif
            %}
          />
          <label for="use_direct_kh"
            >Directly Input Seismic Force for External Stability</label
          >
        </div>
        <div class="input-group">
          <label for="seismic_force"
            >Seismic Horizontal Earth Pressure Force (P):</label
          >
          <input
            type="number"
            id="seismic_force"
            name="seismic_force"
            value="{{ seismic_force }}"
            {%
            if
            not
            use_direct_kh
            %}disabled{%
            endif
            %}
            step="0.01"
            min="0"
          />
          <span class="error-message" id="seismic_force-error"></span>
        </div>
      </div>

      <div id="impact_loads" class="load-input-section">
        <h3>Impact Loads</h3>
        <div id="impact_note" class="impact-note">
          Upper layer rupture impact load = 33.5 kN/m.<br />
          Second layer rupture impact load = 8.8 kN/m.<br />
          Upper layer pullout impact load = 19 kN/m - resisted over full
          length<br />
          Second layer pullout impact load = 8.8 kN/m - resisted over full
          length.
        </div>

        {% for type in ["rupture", "pullout"] %} {% for layer in ["upper",
        "second"] %}
        <div class="input-group">
          <label for="{{ type }}_impact_{{ layer }}"
            >{{ type.capitalize() }} Impact Load - {{ layer.capitalize() }}
            Layer (kN/m):</label
          >
          <input
            type="number"
            id="{{ type }}_impact_{{ layer }}"
            name="{{ type }}_impact_{{ layer }}"
            value="{{ impact_loads[type][layer] }}"
            step="0.01"
            min="0"
          />
          <span
            class="error-message"
            id="{{ type }}_impact_{{ layer }}-error"
          ></span>
        </div>
        {% endfor %} {% endfor %}
      </div>
    </div>
    <button type="submit" id="save-button">Save External Loads</button>
  </div>
</form>

<!-- GRS Wall Visualization Section -->
<section class="visualization-section">
  <h2>Geometry and Loads Visualization</h2>
  <div class="visualization-container">
    <div class="canvas-wrapper">
      <canvas id="geometry2-canvas" width="800" height="500"></canvas>
    </div>
    <div class="button-group">
      <button id="zoom-in-button" class="btn btn-primary">Zoom In</button>
      <button id="zoom-out-button" class="btn btn-primary">Zoom Out</button>
      <button id="fit-button" class="btn btn-primary">Fit to Window</button>
      <button id="screenshot-button" class="btn btn-primary">
        Take Screenshot
      </button>
    </div>
  </div>
</section>

<script>
  function showSection(sectionId) {
    // Hide all sections and remove active class from all buttons
    document.querySelectorAll(".load-input-section").forEach((section) => {
      section.style.display = "none";
    });
    document.querySelectorAll(".tab-button").forEach((button) => {
      button.classList.remove("active");
    });

    // Show selected section and set active class
    document.getElementById(sectionId).style.display = "block";
    document
      .querySelector(`button[onclick="showSection('${sectionId}')"]`)
      .classList.add("active");
  }

  function toggleKhInput() {
    const khInput = document.getElementById("seismic_force");
    const checkbox = document.getElementById("use_direct_kh");
    khInput.disabled = !checkbox.checked;

    if (checkbox.checked) {
      khInput.focus();
    }
  }

  // Validate a single field
  function validateField(input) {
    const value = parseFloat(input.value);
    const errorElement = document.getElementById(`${input.id}-error`);

    if (input.value.trim() === "") {
      // Empty is OK for these fields (defaults to 0)
      errorElement.textContent = "";
      input.classList.remove("invalid");
      return true;
    } else if (isNaN(value)) {
      errorElement.textContent = "Please enter a valid number";
      input.classList.add("invalid");
      return false;
    } else if (
      input.hasAttribute("min") &&
      value < parseFloat(input.getAttribute("min"))
    ) {
      errorElement.textContent = `Value must be at least ${input.getAttribute(
        "min"
      )}`;
      input.classList.add("invalid");
      return false;
    } else if (
      input.hasAttribute("max") &&
      value > parseFloat(input.getAttribute("max"))
    ) {
      errorElement.textContent = `Value must not exceed ${input.getAttribute(
        "max"
      )}`;
      input.classList.add("invalid");
      return false;
    } else {
      errorElement.textContent = "";
      input.classList.remove("invalid");
      return true;
    }
  }

  // Function to load data from localStorage
  function loadFormDataFromLocalStorage() {
    try {
      const savedData = localStorage.getItem("externalloads_data");
      if (savedData) {
        const data = JSON.parse(savedData);
        console.log("Loading external loads data from localStorage:", data);

        // Load dead loads
        if (data.dead_loads) {
          for (let i = 0; i < 3; i++) {
            const field = document.getElementById(`dead_load${i + 1}`);
            if (field && data.dead_loads[i] !== undefined) {
              field.value = data.dead_loads[i];
            }
          }
        }

        // Load live loads
        if (data.live_loads) {
          for (let i = 0; i < 3; i++) {
            const field = document.getElementById(`live_load${i + 1}`);
            if (field && data.live_loads[i] !== undefined) {
              field.value = data.live_loads[i];
            }
          }
        }

        // Load strip loads
        if (data.vertical_strip_load) {
          for (let i = 0; i < 3; i++) {
            const field = document.getElementById(
              `vertical_strip_load${i + 1}`
            );
            if (field && data.vertical_strip_load[i] !== undefined) {
              field.value = data.vertical_strip_load[i];
            }
          }
        }

        if (data.horizontal_strip_load) {
          for (let i = 0; i < 3; i++) {
            const field = document.getElementById(
              `horizontal_strip_load${i + 1}`
            );
            if (field && data.horizontal_strip_load[i] !== undefined) {
              field.value = data.horizontal_strip_load[i];
            }
          }
        }

        if (data.strip_load_width) {
          for (let i = 0; i < 3; i++) {
            const field = document.getElementById(`strip_load_width${i + 1}`);
            if (field && data.strip_load_width[i] !== undefined) {
              field.value = data.strip_load_width[i];
            }
          }
        }

        if (data.strip_load_distance) {
          for (let i = 0; i < 3; i++) {
            const field = document.getElementById(
              `strip_load_distance${i + 1}`
            );
            if (field && data.strip_load_distance[i] !== undefined) {
              field.value = data.strip_load_distance[i];
            }
          }
        }

        // Load earthquake data
        if (data.earthquake_acceleration !== undefined) {
          const field = document.getElementById("earthquake_acceleration");
          if (field) field.value = data.earthquake_acceleration;
        }

        if (data.seismic_force !== undefined) {
          const field = document.getElementById("seismic_force");
          if (field) field.value = data.seismic_force;
        }

        // Load impact loads
        if (data.impact_loads) {
          const types = ["rupture", "pullout"];
          const layers = ["upper", "second"];

          types.forEach((type) => {
            layers.forEach((layer) => {
              if (
                data.impact_loads[type] &&
                data.impact_loads[type][layer] !== undefined
              ) {
                const field = document.getElementById(
                  `${type}_impact_${layer}`
                );
                if (field) field.value = data.impact_loads[type][layer];
              }
            });
          });
        }

        console.log(
          "External loads data loaded from localStorage successfully"
        );
      }
    } catch (error) {
      console.error(
        "Error loading external loads data from localStorage:",
        error
      );
    }
  }

  document.addEventListener("DOMContentLoaded", function () {
    // Show first section by default
    showSection("dead_loads");

    const form = document.getElementById("externalloadsform");

    // Load data from localStorage to restore form values
    loadFormDataFromLocalStorage();

    // Add validation event listeners to all inputs (but not for dynamic updates - handled by externalLoadsVisualization.js)
    form.querySelectorAll('input[type="number"]').forEach((input) => {
      input.addEventListener("blur", () => validateField(input));
    });

    form.addEventListener("submit", function (event) {
      event.preventDefault();

      // Validate all fields
      let isValid = true;
      form.querySelectorAll('input[type="number"]').forEach((input) => {
        if (!validateField(input)) {
          isValid = false;
        }
      });

      if (!isValid) {
        if (typeof showErrorPopup === "function") {
          showErrorPopup("Please fix the errors before submitting the form.");
        } else {
          alert("Please fix the errors before submitting the form.");
        }
        return;
      }

      const formData = new FormData(form);
      fetch("/externalloads", {
        method: "POST",
        body: formData,
      })
        .then((response) => response.json())
        .then((data) => {
          const externalloads_data = {
            dead_loads: [
              parseFloat(document.getElementById("dead_load1").value) || 0,
              parseFloat(document.getElementById("dead_load2").value) || 0,
              parseFloat(document.getElementById("dead_load3").value) || 0,
            ],
            live_loads: [
              parseFloat(document.getElementById("live_load1").value) || 0,
              parseFloat(document.getElementById("live_load2").value) || 0,
              parseFloat(document.getElementById("live_load3").value) || 0,
            ],
            vertical_strip_load: [
              parseFloat(
                document.getElementById("vertical_strip_load1").value
              ) || 0,
              parseFloat(
                document.getElementById("vertical_strip_load2").value
              ) || 0,
              parseFloat(
                document.getElementById("vertical_strip_load3").value
              ) || 0,
            ],
            horizontal_strip_load: [
              parseFloat(
                document.getElementById("horizontal_strip_load1").value
              ) || 0,
              parseFloat(
                document.getElementById("horizontal_strip_load2").value
              ) || 0,
              parseFloat(
                document.getElementById("horizontal_strip_load3").value
              ) || 0,
            ],
            strip_load_width: [
              parseFloat(document.getElementById("strip_load_width1").value) ||
                0,
              parseFloat(document.getElementById("strip_load_width2").value) ||
                0,
              parseFloat(document.getElementById("strip_load_width3").value) ||
                0,
            ],
            strip_load_distance: [
              parseFloat(
                document.getElementById("strip_load_distance1").value
              ) || 0,
              parseFloat(
                document.getElementById("strip_load_distance2").value
              ) || 0,
              parseFloat(
                document.getElementById("strip_load_distance3").value
              ) || 0,
            ],
            earthquake_acceleration:
              parseFloat(
                document.getElementById("earthquake_acceleration").value
              ) || 0,
            seismic_force:
              parseFloat(document.getElementById("seismic_force").value) || 0,
            impact_loads: {
              rupture: {
                upper:
                  parseFloat(
                    document.getElementById("rupture_impact_upper").value
                  ) || 0,
                second:
                  parseFloat(
                    document.getElementById("rupture_impact_second").value
                  ) || 0,
              },
              pullout: {
                upper:
                  parseFloat(
                    document.getElementById("pullout_impact_upper").value
                  ) || 0,
                second:
                  parseFloat(
                    document.getElementById("pullout_impact_second").value
                  ) || 0,
              },
            },
          };
          localStorage.setItem(
            "externalloads_data",
            JSON.stringify(externalloads_data)
          );

          if (typeof showSuccessPopup === "function") {
            showSuccessPopup(
              data.message || "External loads data saved successfully!"
            );
          } else {
            alert(data.message || "External loads data saved successfully!");
          }

          // Update sidebar status indicator
          if (typeof updateSidebarStatus === "function") {
            updateSidebarStatus("externalloadsform");
          }

          // Reinitialize external loads visualization to restore dynamic updates
          if (typeof initializeExternalLoadsVisualization === "function") {
            setTimeout(() => {
              initializeExternalLoadsVisualization();
            }, 100);
          }

          // Also reload form data from localStorage to ensure values persist
          setTimeout(() => {
            loadFormDataFromLocalStorage();
          }, 200);
        })
        .catch((error) => {
          console.error("Error:", error);
          if (typeof showErrorPopup === "function") {
            showErrorPopup("Error saving external loads data.");
          } else {
            alert("Error saving external loads data.");
          }
        });
    });

    // Make function available globally for AJAX reinitialization
    window.loadExternalLoadsFromLocalStorage = loadFormDataFromLocalStorage;
  });
</script>

<script src="{{ url_for('static', filename='js/externalLoadsVisualization.js') }}"></script>

<style>
  .tab-button {
    padding: 10px 15px;
    background-color: #007bff;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s;
  }

  .tab-button:hover {
    background-color: #0056b3;
  }

  .tab-button.active {
    background-color: #0056b3;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    font-weight: bold;
  }

  .load-input-section {
    display: none;
    margin-top: 20px;
  }

  .input-group {
    margin-bottom: 15px;
  }

  .strip-load-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }

  .strip-load-box {
    flex: 1;
    min-width: 200px;
    border: 1px solid #ddd;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .impact-note {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 15px;
    border-left: 4px solid #007bff;
  }

  .error-message {
    color: #d9534f;
    font-size: 0.85em;
    margin-top: 4px;
    display: block;
    font-weight: 500;
  }

  input.invalid {
    border: 1px solid #d9534f;
    background-color: #fff8f8;
  }

  input[type="number"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: border-color 0.3s, box-shadow 0.3s;
  }

  input[type="number"]:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    outline: none;
  }

  /* Visualization Styling */
  .visualization-section {
    background: linear-gradient(145deg, #fdfaf6, #efe7dc);
    border-radius: 16px;
    box-shadow: 8px 8px 20px rgba(0, 0, 0, 0.05),
      -8px -8px 20px rgba(255, 255, 255, 0.8);
    padding: 30px;
    margin: auto;
    margin-top: 30px;
    width: 90%;
    max-width: 900px;
    border: 1px solid #d6c4b1;
  }

  .visualization-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .canvas-wrapper {
    background: linear-gradient(145deg, #f5e6d7, #e6d3b8);
    border-radius: 16px;
    padding: 20px;
    border: 1px solid #c4a785;
    width: 100%;
    max-width: 800px;
    overflow: hidden;
    box-sizing: border-box;
    box-shadow: inset 6px 6px 12px rgba(0, 0, 0, 0.05),
      inset -6px -6px 12px rgba(255, 255, 255, 0.8);
  }

  canvas {
    display: block;
    max-width: 100%;
    height: auto;
    border-radius: 12px;
    background-color: #fff;
    border: 1px solid #c4a785;
  }

  h2 {
    color: #8c5a3c;
    text-align: center;
    margin-bottom: 20px;
  }

  .button-group {
    margin-top: 20px;
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: center;
  }

  #save-button {
    margin-top: 20px;
    padding: 12px 24px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s, transform 0.2s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: block;
    width: 100%;
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
  }

  #save-button:hover {
    background-color: #218838;
    transform: translateY(-1px);
  }

  #save-button:active {
    transform: translateY(0);
  }
</style>
{% endblock %}
