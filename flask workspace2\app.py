from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash, Response, send_file, make_response
from backend import calculate_pressure
import os
from datetime import datetime
from weasyprint import HTML
from flask_session import Session
import base64
import uuid
import glob
from flask_mysqldb import MySQL
    
app = Flask(__name__)
app.config['SESSION_TYPE'] = 'filesystem'
app.secret_key = 'YourSecretKeyHere'  # VERY IMPORTANT! Replace with a real secret key.
Session(app)
data_store = {}  # Temporary storage
app.config['MYSQL_HOST'] = 'localhost'
app.config['MYSQL_USER'] = 'root'
app.config['MYSQL_PASSWORD'] = 'sriroot'
app.config['MYSQL_DB'] = 'grs_software'
mysql = MySQL(app)

# Create users table if not exists (run once, or use a migration tool)
def init_db():
    with app.app_context():
        cur = mysql.connection.cursor()
        # Users table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                password VARCHAR(100) NOT NULL,
                email VARCHAR(100),
                role ENUM('user', 'admin') DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Access requests table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS access_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                full_name VARCHAR(100) NOT NULL,
                email VARCHAR(100) NOT NULL,
                organization VARCHAR(100),
                purpose TEXT NOT NULL,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed_at TIMESTAMP NULL
            )
        """)

        # User sessions table for single-session login
        cur.execute("""
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                session_id VARCHAR(255) NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                ip_address VARCHAR(45),
                user_agent TEXT,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_user_id (user_id),
                INDEX idx_session_id (session_id)
            )
        """)

        # Check if admin exists, create if not
        cur.execute("SELECT * FROM users WHERE role='admin' LIMIT 1")
        admin = cur.fetchone()
        if not admin:
            # Create default admin user (CHANGE THIS PASSWORD IN PRODUCTION!)
            cur.execute("INSERT INTO users (username, password, email, role) VALUES (%s, %s, %s, %s)",
                       ('admin', 'admin123', '<EMAIL>', 'admin'))

        mysql.connection.commit()
        cur.close()
init_db()

# Session management functions
def create_user_session(user_id, username):
    """Create a new session for the user and invalidate any existing sessions"""
    try:
        cur = mysql.connection.cursor()

        # First, delete any existing sessions for this user (single session enforcement)
        cur.execute("DELETE FROM user_sessions WHERE user_id = %s", (user_id,))

        # Generate a unique session ID
        session_id = str(uuid.uuid4())

        # Get client information
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))
        user_agent = request.environ.get('HTTP_USER_AGENT', '')

        # Create new session record
        cur.execute("""
            INSERT INTO user_sessions (user_id, session_id, ip_address, user_agent)
            VALUES (%s, %s, %s, %s)
        """, (user_id, session_id, ip_address, user_agent))

        mysql.connection.commit()
        cur.close()

        # Store session ID in Flask session
        session['session_id'] = session_id
        session['user_id'] = user_id

        return session_id

    except Exception as e:
        print(f"Error creating user session: {e}")
        return None

def validate_user_session():
    """Validate if the current session is still active in the database"""
    if not session.get('session_id') or not session.get('user_id'):
        return False

    try:
        cur = mysql.connection.cursor()
        cur.execute("""
            SELECT us.id, u.username, u.role
            FROM user_sessions us
            JOIN users u ON us.user_id = u.id
            WHERE us.session_id = %s AND us.user_id = %s
        """, (session.get('session_id'), session.get('user_id')))

        result = cur.fetchone()
        cur.close()

        if result:
            # Update last activity
            cur = mysql.connection.cursor()
            cur.execute("""
                UPDATE user_sessions
                SET last_activity = NOW()
                WHERE session_id = %s
            """, (session.get('session_id'),))
            mysql.connection.commit()
            cur.close()
            return True
        else:
            # Session not found in database, clear Flask session
            clear_all_session_data()
            return False

    except Exception as e:
        print(f"Error validating session: {e}")
        return False

def cleanup_user_session(user_id=None, session_id=None):
    """Clean up user session from database"""
    try:
        cur = mysql.connection.cursor()

        if session_id:
            cur.execute("DELETE FROM user_sessions WHERE session_id = %s", (session_id,))
        elif user_id:
            cur.execute("DELETE FROM user_sessions WHERE user_id = %s", (user_id,))

        mysql.connection.commit()
        cur.close()

    except Exception as e:
        print(f"Error cleaning up session: {e}")

def cleanup_old_sessions():
    """Clean up sessions older than 24 hours (optional maintenance function)"""
    try:
        cur = mysql.connection.cursor()
        cur.execute("""
            DELETE FROM user_sessions
            WHERE last_activity < DATE_SUB(NOW(), INTERVAL 24 HOUR)
        """)
        mysql.connection.commit()
        cur.close()

    except Exception as e:
        print(f"Error cleaning up old sessions: {e}")

def clear_temp_session_data():
    """Clear all temporary session data"""
    temp_keys = ['temp_username', 'temp_password']
    for key in temp_keys:
        session.pop(key, None)

def clear_all_session_data():
    """Clear all session data including application data"""
    # List of all possible session keys used in the application
    session_keys = [
        'user', 'user_id', 'session_id', 'is_admin',
        'temp_username', 'temp_password',
        'project_name', 'project_id', 'designer', 'client', 'description', 'date', 'revision',
        'geometryData', 'geometry_data_saved',
        'soil_density', 'friction_angle', 'cohesion',
        'retainedsoil_density', 'retainedfriction_angle', 'retainedcohesion', 'retainedsoil_data_saved',
        'foundationsoildensity', 'foundationsoilfriction_angle', 'foundationsoilcohesion',
        'eccentricity', 'eccentricity_seismic', 'watertable', 'foundationsoil_data_saved',
        'externalloads_data',
        'reinforcement_data', 'reinforcementproperties_data_saved',
        'reinforcement_layout_data', 'reinforcementlayout_data_saved',
        'current_screenshot_id', 'analysis_results'
    ]

    for key in session_keys:
        session.pop(key, None)

    # Clear any remaining keys
    session.clear()

def delete_session_file():
    """Physically delete the session file from filesystem"""
    try:
        # Get the session ID from the cookie
        session_cookie_name = app.config.get('SESSION_COOKIE_NAME', 'session')
        session_id = request.cookies.get(session_cookie_name)
        if session_id:
            # Flask-Session stores files with session_ prefix
            session_file_pattern = os.path.join(app.config.get('SESSION_FILE_DIR', 'flask_session'), f'session_{session_id}*')

            # Find and delete all matching session files
            for session_file in glob.glob(session_file_pattern):
                try:
                    os.remove(session_file)
                    print(f"Deleted session file: {session_file}")
                except OSError as e:
                    print(f"Error deleting session file {session_file}: {e}")

            # Also try alternative naming patterns
            alt_patterns = [
                os.path.join(app.config.get('SESSION_FILE_DIR', 'flask_session'), f'{session_id}*'),
                os.path.join(app.config.get('SESSION_FILE_DIR', 'flask_session'), f'*{session_id}*')
            ]

            for pattern in alt_patterns:
                for session_file in glob.glob(pattern):
                    try:
                        os.remove(session_file)
                        print(f"Deleted session file: {session_file}")
                    except OSError as e:
                        print(f"Error deleting session file {session_file}: {e}")

    except Exception as e:
        print(f"Error in delete_session_file: {e}")

def force_clear_all_session_files():
    """Nuclear option: clear all session files (use with caution)"""
    try:
        session_dir = app.config.get('SESSION_FILE_DIR', 'flask_session')
        if os.path.exists(session_dir):
            # Remove all files in the session directory
            for filename in os.listdir(session_dir):
                file_path = os.path.join(session_dir, filename)
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        print(f"Deleted session file: {file_path}")
                except OSError as e:
                    print(f"Error deleting {file_path}: {e}")
    except Exception as e:
        print(f"Error in force_clear_all_session_files: {e}")

@app.after_request
def add_cache_control_headers(response):
    """Add cache control headers to prevent caching of sensitive pages"""
    # Don't cache any pages for logged-in users
    if session.get('user'):
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
    return response

@app.before_request
def require_login():
    allowed_routes = ['login', 'request_access', 'static', 'clear_temp_session', 'logout_all_sessions']
    admin_routes = ['admin_dashboard', 'admin_access_requests', 'process_request', 'admin_active_sessions', 'admin_force_logout', 'admin_clear_session_data', 'admin_clear_all_session_files', 'test_json']

    # Check if the requested endpoint requires authentication
    if request.endpoint not in allowed_routes:
        # Check if user is logged in
        if not session.get('user'):
            return redirect(url_for('login'))

        # Validate session in database (single-session enforcement)
        if not validate_user_session():
            clear_all_session_data()
            return redirect(url_for('login'))

        # Check if admin is trying to access admin routes
        if request.endpoint in admin_routes or 'admin' in request.path:
            if not session.get('is_admin'):
                flash('Unauthorized access', 'danger')
                return redirect(url_for('home'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        force_login = request.form.get('force_login', False)

        cur = mysql.connection.cursor()
        cur.execute("SELECT * FROM users WHERE username=%s", (username,))
        user = cur.fetchone()
        cur.close()

        if user and user[2] == password:  # Compare plain text password
            user_id = user[0]

            # Check if user already has an active session
            cur = mysql.connection.cursor()
            cur.execute("SELECT COUNT(*) FROM user_sessions WHERE user_id = %s", (user_id,))
            active_sessions_count = cur.fetchone()[0]
            cur.close()

            if active_sessions_count > 0 and not force_login:
                # User has active sessions, ask them to logout first
                # Temporarily store credentials for force login option
                session['temp_username'] = username
                session['temp_password'] = password

                # Check if this is an AJAX request
                if request.headers.get('Content-Type') == 'application/json' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'status': 'multi_session', 'message': 'Multiple sessions detected', 'username': username})
                else:
                    return render_template('login.html', show_force_login=True, username=username)

            # Either no active sessions OR user chose to force login
            # Create new session (this will invalidate any existing sessions for this user)
            session_id = create_user_session(user_id, username)

            if session_id:
                session['user'] = username
                session['is_admin'] = (user[4] == 'admin')  # Check if user is admin

                # Clean up temporary credentials
                clear_temp_session_data()

                # Only show login success message for force login
                if force_login:
                    flash('Welcome back! Previous sessions terminated.', 'success')

                # Redirect admin to admin dashboard, regular users to home
                if session['is_admin']:
                    return redirect(url_for('admin_dashboard'))
                else:
                    return redirect(url_for('home'))
            else:
                # Session creation failed - don't show popup, use flash message
                flash('Login failed. Please try again.', 'danger')
        else:
            # Invalid credentials - show popup for AJAX requests
            if request.headers.get('Content-Type') == 'application/json' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'status': 'error', 'message': 'Invalid username or password'})
            else:
                flash('Invalid username or password', 'danger')

    # Check if we should show force login page (for GET requests after multi-session detection)
    if session.get('temp_username') and session.get('temp_password'):
        return render_template('login.html', show_force_login=True, username=session.get('temp_username'))

    return render_template('login.html')

@app.route('/clear_temp_session')
def clear_temp_session():
    """Clear temporary session data and redirect to login"""
    clear_temp_session_data()
    return redirect(url_for('login'))

@app.route('/logout_all_sessions', methods=['POST'])
def logout_all_sessions():
    """Allow user to logout all their sessions remotely"""
    username = request.form.get('username')
    password = request.form.get('password')

    if not username or not password:
        flash('Username and password are required.', 'danger')
        return redirect(url_for('login'))

    # Verify credentials
    cur = mysql.connection.cursor()
    cur.execute("SELECT * FROM users WHERE username=%s", (username,))
    user = cur.fetchone()
    cur.close()

    if user and user[2] == password:
        user_id = user[0]
        # Logout all sessions for this user
        cleanup_user_session(user_id=user_id)
        flash('All sessions have been logged out successfully. You can now login.', 'success')
    else:
        flash('Invalid username or password.', 'danger')

    return redirect(url_for('login'))

@app.route('/logout')
def logout():
    # Clean up session from database
    if session.get('session_id'):
        cleanup_user_session(session_id=session.get('session_id'))

    # Delete the physical session file from filesystem
    delete_session_file()

    # Clear ALL Flask session data completely
    clear_all_session_data()

    # Create response with cache control headers
    response = make_response(redirect(url_for('login')))
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'

    return response


# @app.route('/register', methods=['GET', 'POST'])
# def register():
    # if request.method == 'POST':
    #     username = request.form['username']
    #     password = request.form['password']
    #     try:
    #         cur = mysql.connection.cursor()
    #         cur.execute("INSERT INTO users (username, password) VALUES (%s, %s)", (username, password))
    #         mysql.connection.commit()
    #         cur.close()
    #         flash('Registration successful! Please log in.', 'success')
    #         return redirect(url_for('login'))
    #     except Exception as e:
    #         print("Registration error:", e)
    #         if hasattr(e, 'args') and len(e.args) > 1 and '1062' in str(e.args[1]):
    #             flash('Username already exists.', 'danger')
    #         else:
    #             flash('Registration failed. Please try again.', 'danger')
    #         try:
    #             cur.close()
    #         except:
    #             pass
    # return render_template('register.html')
    # return redirect(url_for('request_access'))

@app.route('/request_access', methods=['GET', 'POST'])
def request_access():
    if request.method == 'POST':
        # Check if this is an AJAX request
        if request.headers.get('Content-Type') == 'application/json':
            data = request.get_json()
            full_name = data['full_name']
            email = data['email']
            organization = data['organization']
            purpose = data['purpose']
        else:
            full_name = request.form['full_name']
            email = request.form['email']
            organization = request.form['organization']
            purpose = request.form['purpose']

        try:
            cur = mysql.connection.cursor()
            # Check if this email already has a pending or approved request
            cur.execute("SELECT * FROM access_requests WHERE email = %s AND status IN ('pending', 'approved')", (email,))
            existing_request = cur.fetchone()

            if existing_request:
                if existing_request[5] == 'pending':
                    message = 'You already have a pending access request. We will contact you when it is processed.'
                    status = 'info'
                else:
                    message = 'Your access request was already approved. Please check your email for credentials.'
                    status = 'info'
            else:
                # Insert new request
                cur.execute("""
                    INSERT INTO access_requests (full_name, email, organization, purpose)
                    VALUES (%s, %s, %s, %s)
                """, (full_name, email, organization, purpose))
                mysql.connection.commit()
                message = 'Your access request has been submitted. We will contact you soon.'
                status = 'success'

            cur.close()

            # Return JSON response for AJAX requests
            if request.headers.get('Content-Type') == 'application/json':
                return jsonify({'status': status, 'message': message})
            else:
                # Fallback for non-AJAX requests
                flash(message, status)
                return redirect(url_for('login'))

        except Exception as e:
            print("Request access error:", e)
            error_message = 'An error occurred. Please try again.'
            try:
                cur.close()
            except:
                pass

            # Return JSON response for AJAX requests
            if request.headers.get('Content-Type') == 'application/json':
                return jsonify({'status': 'error', 'message': error_message})
            else:
                flash(error_message, 'danger')

    return render_template('request_access.html')

@app.route('/admin/access_requests')
def admin_access_requests():
    if not session.get('user') or not session.get('is_admin'):
        flash('Unauthorized access', 'danger')
        return redirect(url_for('home'))

    cur = mysql.connection.cursor()
    # Join with users table to get username and password for approved requests
    cur.execute("""
        SELECT ar.*, u.username, u.password
        FROM access_requests ar
        LEFT JOIN users u ON ar.email = u.email
        ORDER BY ar.created_at DESC
    """)
    requests = cur.fetchall()
    cur.close()

    return render_template('admin_access_requests.html', requests=requests)

@app.route('/admin/process_request/<int:request_id>', methods=['POST'])
def process_request(request_id):
    if not session.get('user') or not session.get('is_admin'):
        return jsonify({'status': 'error', 'message': 'Unauthorized'}), 403
    
    action = request.form.get('action')
    if action not in ['approve', 'reject']:
        return jsonify({'status': 'error', 'message': 'Invalid action'}), 400
    
    try:
        cur = mysql.connection.cursor()
        
        # Get the request details
        cur.execute("SELECT * FROM access_requests WHERE id = %s", (request_id,))
        access_req = cur.fetchone()
        
        if not access_req:
            return jsonify({'status': 'error', 'message': 'Request not found'}), 404
        
        if action == 'approve':
            # Generate username and password
            import random
            import string
            
            username = access_req[1].split()[0].lower() + str(random.randint(100, 999))
            password = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
            
            # Create user account
            cur.execute("INSERT INTO users (username, password, email) VALUES (%s, %s, %s)", 
                      (username, password, access_req[2]))
            
            # Update request status
            cur.execute("UPDATE access_requests SET status = 'approved', processed_at = NOW() WHERE id = %s", 
                      (request_id,))
            
            # In a real application, you would send an email with credentials
            flash(f"Access granted for {access_req[1]}. Username: {username}, Password: {password}", 'success')
            
        else:  # reject
            cur.execute("UPDATE access_requests SET status = 'rejected', processed_at = NOW() WHERE id = %s", 
                      (request_id,))
            
        mysql.connection.commit()
        cur.close()
        
        return jsonify({'status': 'success'})
        
    except Exception as e:
        print("Error processing request:", e)
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/')
def home():
    # Check if user is admin and redirect to admin dashboard
    if session.get('is_admin'):
        return redirect(url_for('admin_dashboard'))

    # Regular users see the home page
    return render_template('home.html')

@app.route('/project_info', methods=['GET', 'POST'])
def project_info():
    if request.method == 'POST':
        # Process form data
        project_name = request.form.get('project_name')
        project_id = request.form.get('project_id')
        designer = request.form.get('designer')
        client = request.form.get('client')
        description = request.form.get('description')
        date = request.form.get('date')
        revision = request.form.get('revision')
        
        # Save data to session
        session['project_name'] = project_name
        session['project_id'] = project_id
        session['designer'] = designer
        session['client'] = client
        session['description'] = description
        session['date'] = date
        session['revision'] = revision
        
        flash('Project info saved successfully', 'success')
        return jsonify({'message': 'Project info saved successfully!'})
        
    # Handle GET request
    return render_template('project_info.html',
                           project_name=session.get('project_name', ''),
                           project_id=session.get('project_id', ''),
                           designer=session.get('designer', ''),
                           client=session.get('client', ''),
                           description=session.get('description', ''),
                           date=session.get('date', ''),
                           revision=session.get('revision', ''))


@app.route('/geometry', methods=['GET', 'POST'])
def geometry():
    if request.method == 'POST':
        # Get form data
        geometry_data = {
            'wallHeight': request.form.get('wall_height'),
            'embedmentDepth': request.form.get('embedment_depth'),
            'wallLength': request.form.get('wall_length'),
            'wallBatter': request.form.get('wall_batter'),
            'backslopeAngle': request.form.get('backslope_angle'),
            'backslopeRise': request.form.get('backslope_rise')
        }

        # Save data to session
        # Save the object to session
        session['geometryData'] = geometry_data
        session['geometry_data_saved'] = True
        flash('Geometry Data saved successfully', 'success')
        return jsonify({'message': 'Geometry data saved successfully!'})
        
    # Retrieve and pass data for GET request
    geometry_data = session.get('geometryData', {})
    return render_template('geometry.html', **geometry_data)



@app.route('/reinforcedsoil', methods=['GET', 'POST'])
def reinforcedsoil():
    if request.method == 'POST':
        # Getting the form data
        soil_density = request.form.get('soil_density')
        friction_angle = request.form.get('friction_angle')
        cohesion = request.form.get('cohesion')
        
        # Save data to session
        session['soil_density'] = soil_density
        session['friction_angle'] = friction_angle
        session['cohesion'] = cohesion
       # session['reinforcedsoil_data_saved'] = True
        
        # Return success message
            # Return JSON response
        flash('Reinforced soil data saved successfully!', 'success')
        return jsonify({'message': 'Reinforced soil data saved successfully!'})

    
    # Display form with session data
    return render_template('reinforcedsoil.html', 
                            soil_density=session.get('soil_density', ''),
                            friction_angle=session.get('friction_angle', ''),
                            cohesion=session.get('cohesion', ''))


@app.route('/retainedsoil', methods=['GET', 'POST'])
def retainedsoil():
    if request.method == 'POST':
        # Getting the form data from the request
        retainedsoil_density = request.form.get('retainedsoil_density')
        retainedfriction_angle = request.form.get('retainedfriction_angle')
        retainedcohesion = request.form.get('retainedcohesion')
        
        # Save the data to the session (or to a database, if needed)
        session['retainedsoil_density'] = retainedsoil_density
        session['retainedfriction_angle'] = retainedfriction_angle
        session['retainedcohesion'] = retainedcohesion
        session['retainedsoil_data_saved'] = True
        
        # Set a message that data has been saved
        flash('Retained soil data has been saved!', 'success')
        # Return a response (can return a message or redirect)
       # return redirect(url_for('retainedsoil'))
        return jsonify({'message': 'Reatined soil data saved successfully!'})
        
    # Handle GET request to display the form with data from session
    return render_template('retainedsoil.html', 
                           retainedsoil_density=session.get('retainedsoil_density', ''),
                           retainedfriction_angle=session.get('retainedfriction_angle', ''),
                           retainedcohesion=session.get('retainedcohesion', ''))

@app.route('/foundationsoil', methods=['GET', 'POST'])
def foundationsoil():
    if request.method == 'POST':
        # Getting the form data from the request
        foundationsoildensity = request.form.get('foundationsoildensity')
        foundationsoilfriction_angle = request.form.get('foundationsoilfriction_angle')
        foundationsoilcohesion = request.form.get('foundationsoilcohesion')
        eccentricity = request.form.get('eccentricity')
        eccentricity_seismic = request.form.get('eccentricity_seismic')
        watertable = request.form.get('watertable')

        # Save the data to the session (or to a database, if needed)
        session['foundationsoildensity'] = foundationsoildensity
        session['foundationsoilfriction_angle'] = foundationsoilfriction_angle
        session['foundationsoilcohesion'] = foundationsoilcohesion
        session['eccentricity'] = eccentricity
        session['eccentricity_seismic'] = eccentricity_seismic
        session['watertable'] = watertable
        session['foundationsoil_data_saved'] = True

        # Set a message that data has been saved
        flash('Foundation soil data has been saved!', 'success')
        
        # Return a response (redirect back to the same page)
        return jsonify({'message': 'Foundation soil data saved successfully!'})

    # Handle GET request to display the form with session data
    return render_template('foundationsoil.html', 
                           density=session.get('foundationsoildensity', ''),
                           friction_angle=session.get('foundationsoilfriction_angle', ''),
                           cohesion=session.get('foundationsoilcohesion', ''),
                           eccentricity=session.get('eccentricity', ''),
                           eccentricity_seismic=session.get('eccentricity_seismic', ''),
                           watertable=session.get('watertable', ''))


@app.route('/externalloads', methods=['GET', 'POST'])
def externalloads():
    # Default values for form fields
    default_strip_load = [""] * 3
    default_impact_loads = {
        "rupture": {"upper": "", "second": ""},
        "pullout": {"upper": "", "second": ""}
    }

    if request.method == 'POST':
        try:
            # Getting form data
            dead_loads = [request.form.get(f'dead_load{i}', '') for i in range(1, 4)]
            live_loads = [request.form.get(f'live_load{i}', '') for i in range(1, 4)]
            vertical_strip_load = [request.form.get(f'vertical_strip_load{i}', '') for i in range(1, 4)]
            horizontal_strip_load = [request.form.get(f'horizontal_strip_load{i}', '') for i in range(1, 4)]
            strip_load_width = [request.form.get(f'strip_load_width{i}', '') for i in range(1, 4)]
            strip_load_distance = [request.form.get(f'strip_load_distance{i}', '') for i in range(1, 4)]
            earthquake_acceleration = request.form.get('earthquake_acceleration', '')
            use_direct_kh = request.form.get('use_direct_kh', False)
            
            seismic_force = request.form.get('seismic_force', '')

            impact_loads = {
                "rupture": {
                    "upper": request.form.get('rupture_impact_upper', ''),
                    "second": request.form.get('rupture_impact_second', '')
                },
                "pullout": {
                    "upper": request.form.get('pullout_impact_upper', ''),
                    "second": request.form.get('pullout_impact_second', '')
                }
            }

            # Convert to floats
            dead_loads = [float(x) if x else 0 for x in dead_loads]
            live_loads = [float(x) if x else 0 for x in live_loads]
            vertical_strip_load = [float(x) if x else 0 for x in vertical_strip_load]
            horizontal_strip_load = [float(x) if x else 0 for x in horizontal_strip_load]
            strip_load_width = [float(x) if x else 0 for x in strip_load_width]
            strip_load_distance = [float(x) if x else 0 for x in strip_load_distance]
            earthquake_acceleration = float(earthquake_acceleration) if earthquake_acceleration else 0
            use_direct_kh = 'use_direct_kh' in request.form
            seismic_force = float(seismic_force) if seismic_force and use_direct_kh else 0
            impact_loads = {
                "rupture": {
                    "upper": float(impact_loads["rupture"]["upper"]) if impact_loads["rupture"]["upper"] else 0,
                    "second": float(impact_loads["rupture"]["second"]) if impact_loads["rupture"]["second"] else 0
                },
                "pullout": {
                    "upper": float(impact_loads["pullout"]["upper"]) if impact_loads["pullout"]["upper"] else 0,
                    "second": float(impact_loads["pullout"]["second"]) if impact_loads["pullout"]["second"] else 0
                }
            }



            # Store data in session
            session['externalloads_data'] = {
                'dead_loads': dead_loads,
                'live_loads': live_loads,
                'vertical_strip_load': vertical_strip_load,
                'horizontal_strip_load': horizontal_strip_load,
                'strip_load_width': strip_load_width,
                'strip_load_distance': strip_load_distance,
                'earthquake_acceleration': earthquake_acceleration,
                'use_direct_kh': use_direct_kh,
                'seismic_force': seismic_force,
                'impact_loads': impact_loads
            }
            session.modified = True

            return jsonify({'message': 'External loads data has been saved!'})
        except Exception as e:
            return jsonify({'message': f"Error saving external loads data: {str(e)}"}), 400

    # Load data from session
    externalloads_data = session.get('externalloads_data', {})
    default_strip_load = [""] * 3
    default_impact_loads = {
        "rupture": {"upper": "", "second": ""},
        "pullout": {"upper": "", "second": ""}
    }

    # Provide session info
    return render_template('externalloads.html',
                           dead_loads=externalloads_data.get('dead_loads', [""] * 3),
                           live_loads=externalloads_data.get('live_loads', [""] * 3),
                           vertical_strip_load=externalloads_data.get('vertical_strip_load', default_strip_load),
                           horizontal_strip_load=externalloads_data.get('horizontal_strip_load', default_strip_load),
                           strip_load_width=externalloads_data.get('strip_load_width', default_strip_load),
                           strip_load_distance=externalloads_data.get('strip_load_distance', default_strip_load),
                           earthquake_acceleration=externalloads_data.get('earthquake_acceleration', ''),
                           use_direct_kh=externalloads_data.get('use_direct_kh', False),
                           seismic_force=externalloads_data.get('seismic_force', ''),
                           impact_loads=externalloads_data.get('impact_loads', default_impact_loads))




@app.route('/reinforcementproperties', methods=['GET', 'POST'])
def reinforcementproperties():
    if request.method == 'POST':
        try:
            # Extract multiple reinforcement rows
            reinforcement_data = []
            row_count = int(request.form.get('row_count', 0))  # Get number of rows

            for i in range(row_count):
                reinforcement_data.append({
                    'type_id': request.form.get(f'type_id_{i}', ''),  # Providing default
                    'name': request.form.get(f'name_{i}', ''),    # defaults to prevent
                    'tult': request.form.get(f'tult_{i}', ''),    # key errors
                    'rfid': request.form.get(f'rfid_{i}', ''),
                    'rfw': request.form.get(f'rfw_{i}', ''),
                    'rfcr': request.form.get(f'rfcr_{i}', ''),
                    'fs': request.form.get(f'fs_{i}', ''),
                    'pullout_angle': request.form.get(f'pullout_angle_{i}', ''),
                    'sliding_angle': request.form.get(f'sliding_angle_{i}', ''),
                    'scale_factor': request.form.get(f'scale_factor_{i}', '')
                })

            # Save in session to retain data
            session['reinforcement_data'] = reinforcement_data
            session['reinforcementproperties_data_saved'] = True

            # Flash success message
            flash('Reinforcement properties saved successfully!', 'success')
            
            # Return JSON response for AJAX compatibility
            return jsonify({
                'status': 'success',
                'message': 'Reinforcement properties saved successfully!',
               # 'data': reinforcement_data  # Optional: Return the saved data
            })

        except Exception as e:
            # Log the error for debugging
            app.logger.error(f"Error saving reinforcement properties data: {str(e)}")
            
            # Flash error message
            flash(f"Error saving reinforcement properties data: {str(e)}", 'error')
            
            # Return JSON response with error details
            return jsonify({
                'status': 'error',
                'message': f"Error saving reinforcement properties data: {str(e)}"
            }), 400  # HTTP 400 for client-side errors

    # Retrieve stored reinforcement data
    reinforcement_data = session.get('reinforcement_data', [])

    # Render the template with the reinforcement data
    return render_template('reinforcementproperties.html', reinforcement_data=reinforcement_data)


@app.route('/reinforcementlayout', methods=['GET', 'POST'])
def reinforcementlayout():
    if request.method == 'POST':
        # Process the data submitted by the form
        data = request.get_json()
        session['reinforcement_layout_data'] = data # Save the data to the session
        session['reinforcementlayout_data_saved'] = True

        flash('Reinforcement layout data saved successfully!', 'success')
        return jsonify({'message': 'Reinforcement layout data saved successfully!'})

    # Handle GET request to display the form
    return render_template('reinforcementlayout.html')


@app.route('/reinforcementlayout/data', methods=['GET'])
def reinforcementlayout_data():
    # Retrieve the data from the session (or database)
    data = session.get('reinforcement_layout_data', [])

    return jsonify(data)


@app.context_processor
def inject_reinforcement_data():
    reinforcement_data = session.get('reinforcement_data', [])
    return dict(reinforcement_data=reinforcement_data)

@app.route('/run_analysis_page')
def run_analysis_page():
    return render_template('run_analysis.html')

@app.route('/run_analysis', methods=['POST'])
def run_analysis():
    required_data = [
        'project_name', 'geometryData', 'soil_density', 'friction_angle', 'cohesion',
        'retainedsoil_density', 'retainedfriction_angle', 'retainedcohesion',
        'foundationsoildensity', 'foundationsoilfriction_angle', 'foundationsoilcohesion',
        'externalloads_data',
        'reinforcement_data', 'reinforcement_layout_data'
    ]
    # Extract geometry data
    geometry_data = session.get('geometryData', {})
    converted_geometry_data = {
        'wall_height': geometry_data.get('wallHeight'),
        'embedment_depth': geometry_data.get('embedmentDepth'),
        'wall_length': geometry_data.get('wallLength'),
        'wall_batter': geometry_data.get('wallBatter'),
        'backslope_angle': geometry_data.get('backslopeAngle'),
        'backslope_rise': geometry_data.get('backslopeRise'),
    }

    # Extract other data from the session
    input_values = {key: session.get(key) for key in required_data}
    input_values.update(converted_geometry_data)

    # Check for missing required data
    # input_values = {key: session.get(key) for key in required_data}
    missing_data = [key for key in required_data if not session.get(key)]
    print("Input values used for calculation:", input_values)
    print("Session data:", dict(session))
    print("Final Input Values:", input_values)
    if missing_data:
        return jsonify({'error': 'Missing required data in session', 'missing_fields': missing_data}), 400

    # Validate externalloads_data
    externalloads_data = session.get('externalloads_data')
    if not externalloads_data:
        return jsonify({'error': 'Missing externalloads_data', 'missing_fields': ['externalloads_data']}), 400

    external_loads_keys = [
        'dead_loads', 'live_loads', 'vertical_strip_load', 'horizontal_strip_load',
        'strip_load_width', 'strip_load_distance', 'earthquake_acceleration',
        'seismic_force', 'impact_loads'
    ]

    for key in external_loads_keys:
        if key not in externalloads_data:
            return jsonify({'error': f'Missing key {key} in externalloads_data', 'missing_fields': [key]}), 400

        # Special handling for impact_loads
        if key == 'impact_loads':
            impact_loads = externalloads_data[key]
            if not isinstance(impact_loads, dict):
                return jsonify({'error': 'impact_loads must be a dictionary', 'missing_fields': [key]}), 400

            # Validate nested structure
            for load_type in ['pullout', 'rupture']:
                if load_type not in impact_loads:
                    return jsonify({'error': f'Missing load type {load_type} in impact_loads', 'missing_fields': [key]}), 400

                for layer in ['upper', 'second']:
                    if layer not in impact_loads[load_type]:
                        return jsonify({'error': f'Missing layer {layer} in impact_loads[{load_type}]', 'missing_fields': [key]}), 400

                    value = impact_loads[load_type][layer]
                    if not isinstance(value, (int, float)):
                        return jsonify({'error': f'Non-numeric value in impact_loads[{load_type}][{layer}]', 'missing_fields': [key]}), 400
        else:
            # For other keys (lists or single numeric values)
            if isinstance(externalloads_data[key], list):
                if len(externalloads_data[key]) != 3:  # Assuming all lists should have 3 elements
                    return jsonify({'error': f'Invalid length for {key}', 'missing_fields': [key]}), 400

                # Check if all elements are numeric (including 0.0)
                if not all(isinstance(x, (int, float)) for x in externalloads_data[key]):
                    return jsonify({'error': f'Non-numeric values in {key}', 'missing_fields': [key]}), 400
            else:
                # For non-list values (e.g., earthquake_acceleration, seismic_force)
                if not isinstance(externalloads_data[key], (int, float)):
                    return jsonify({'error': f'Non-numeric value for {key}', 'missing_fields': [key]}), 400

    # Run the analysis using backend.py
    results = calculate_pressure(session)
    if results is not None and isinstance(results, Response):
        # Store the response content in the session
        session['analysis_results'] = results.get_json()
        return jsonify({
            'message': 'Analysis completed successfully',
            'has_results': True
        })
    else:
        return jsonify({
            'error': 'Error occurred during calculation',
            'has_results': False
        }), 400
    


@app.route('/external_stability_results')
def external_stability_results():
    results = session.get('analysis_results')
    if not results:
        return redirect(url_for('home'))
    return render_template('external_stability_results.html', results=results)

@app.route('/internal_stability_results')
def internal_stability_results():
    results = session.get('analysis_results')
    if not results:
        return redirect(url_for('home'))
    return render_template('internal_stability_results.html', results=results)







# Dictionary to store screenshots in memory (or use a database in production)
layout_screenshots = {}

def add_padding(base64_string):
    """Add padding to base64 string if needed."""
    return base64_string + '=' * (-len(base64_string) % 4)


@app.route('/store-screenshot', methods=['POST'])
def store_screenshot():
    try:
        data = request.get_json()
        if not data or 'screenshot' not in data:
            return jsonify({'status': 'error', 'message': 'Invalid data'}), 400

        full_data_url = data['screenshot']

        # Split data URL into header and base64 data
        if ',' not in full_data_url:
            return jsonify({'status': 'error', 'message': 'Invalid data URL format'}), 400

        header, base64_data = full_data_url.split(",", 1)

        # Validate and pad base64 data
        try:
            base64.b64decode(base64_data, validate=True)
        except Exception as e:
            return jsonify({'status': 'error', 'message': f'Invalid base64: {str(e)}'}), 400

        padded_data = add_padding(base64_data)
        processed_data_url = f"{header},{padded_data}"

        # Generate ID and store
        screenshot_id = str(uuid.uuid4())  # Generate ID BEFORE using it
        layout_screenshots[screenshot_id] = processed_data_url

        # Store ID in session
        session['current_screenshot_id'] = screenshot_id
        return jsonify({'status': 'success', 'screenshot_id': screenshot_id})

    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500


@app.route('/capture-layout-screenshot', methods=['POST'])
def capture_layout_screenshot():
    """Endpoint to trigger automatic screenshot capture from reinforcement layout"""
    try:
        # This endpoint will be called by JavaScript to automatically capture
        # a screenshot when needed for report generation
        return jsonify({'status': 'success', 'message': 'Screenshot capture triggered'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500



@app.route('/generate_report')
def generate_report():
    project_info = {
        'project_name': session.get('project_name', ''),
        'project_id': session.get('project_id', ''),
        'designer': session.get('designer', ''),
        'client': session.get('client', ''),
        'date': session.get('date', ''),
        'description': session.get('description', ''),
        'revision': session.get('revision', ''),
    }
    geometry_data = session.get('geometryData', {})
    geometry = {
        'wall_height': geometry_data.get('wallHeight', ''),
        'wall_length': geometry_data.get('wallLength', ''),
        'wall_batter': geometry_data.get('wallBatter', ''),
        'backslope_angle': geometry_data.get('backslopeAngle', ''),
        'backslope_rise': geometry_data.get('backslopeRise', ''),
        'embedment_depth': geometry_data.get('embedmentDepth', ''),
    }

    soil_properties = {
        'reinforced_soil': {
            'density': session.get('soil_density', ''),
            'friction_angle': session.get('friction_angle', ''),
            'cohesion': session.get('cohesion', ''),
        },
        'retained_soil': {
            'density': session.get('retainedsoil_density', ''),
            'friction_angle': session.get('retainedfriction_angle', ''),
            'cohesion': session.get('retainedcohesion', ''),
        },
        'foundation_soil': {
            'density': session.get('foundationsoildensity', ''),
            'friction_angle': session.get('foundationsoilfriction_angle', ''),
            'cohesion': session.get('foundationsoilcohesion', ''),
            'eccentricity': session.get('eccentricity', ''),
            'eccentricity_seismic': session.get('eccentricity_seismic', ''),
            'watertable': session.get('watertable', ''),
        },
    }
    external_loads = session.get('externalloads_data', {})
    reinforcement_data = session.get('reinforcement_data', [])
    reinforcement_layout = session.get('reinforcement_layout_data', {})
    analysis_results = session.get('analysis_results', {})

        # Retrieve and process screenshot
    screenshot_id = session.get('current_screenshot_id')
    processed_data_url = None

    if screenshot_id:
        full_data_url = layout_screenshots.get(screenshot_id)
        if full_data_url:
            # Split and re-pad the base64 data
            header, base64_data = full_data_url.split(",", 1)
            padded_data = add_padding(base64_data)
            processed_data_url = f"{header},{padded_data}"

    # If no screenshot is available, create a placeholder or use None
    if not processed_data_url:
        print("Warning: No screenshot available for report generation")
        # Create a simple placeholder image data URL
        placeholder_svg = '''<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#f8f9fa"/>
            <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="24" fill="#6c757d">
                No Visualization Screenshot Available
            </text>
            <text x="50%" y="60%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="16" fill="#6c757d">
                Please take a screenshot from the Reinforcement Layout section
            </text>
        </svg>'''
        import base64
        placeholder_b64 = base64.b64encode(placeholder_svg.encode('utf-8')).decode('utf-8')
        processed_data_url = f"data:image/svg+xml;base64,{placeholder_b64}"
    

    html = render_template('report.html',
                           project_info=project_info,
                           geometry=geometry,
                           soil_properties=soil_properties,
                           external_loads=external_loads,
                           reinforcement_data=reinforcement_data,
                           reinforcement_layout=reinforcement_layout,
                           external_stability=session.get('analysis_results', {}),
                           
                           internal_stability={
                               'Reinforcement Results': analysis_results.get('Reinforcement Results', []),
                               'EQ Reinforcement Results': analysis_results.get('EQ Reinforcement Results', []),
                           },
                           
                           generation_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                           layout_screenshot=processed_data_url                   )
    
   

                         
    
    
    # Generate PDF using WeasyPrint
    pdf = HTML(string=html).write_pdf()
    
    response = make_response(pdf)
    response.headers['Content-Type'] = 'application/pdf'
  #  response.headers['Content-Disposition'] = 'attachment; filename=report.pdf'
    response.headers['Content-Disposition'] = f'attachment; filename=GRS_Report_{project_info["project_name"]}.pdf'
    return response


# Add a new route for admin dashboard
@app.route('/admin/dashboard')
def admin_dashboard():
    if not session.get('user') or not session.get('is_admin'):
        flash('Unauthorized access', 'danger')
        return redirect(url_for('login'))

    # Get count of pending access requests for dashboard
    cur = mysql.connection.cursor()
    cur.execute("SELECT COUNT(*) FROM access_requests WHERE status = 'pending'")
    pending_count = cur.fetchone()[0]
    cur.close()

    from datetime import datetime
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    return render_template('admin_dashboard.html', pending_count=pending_count, current_time=current_time)

@app.route('/admin/active_sessions')
def admin_active_sessions():
    if not session.get('user') or not session.get('is_admin'):
        flash('Unauthorized access', 'danger')
        return redirect(url_for('login'))

    # Get all active sessions
    cur = mysql.connection.cursor()
    cur.execute("""
        SELECT us.id, u.username, us.created_at, us.last_activity, us.ip_address, us.user_agent
        FROM user_sessions us
        JOIN users u ON us.user_id = u.id
        ORDER BY us.last_activity DESC
    """)
    active_sessions = cur.fetchall()
    cur.close()

    return render_template('admin_active_sessions.html', active_sessions=active_sessions)

@app.route('/admin/force_logout/<int:session_db_id>', methods=['POST'])
def admin_force_logout(session_db_id):
    print(f"Force logout called for session ID: {session_db_id}")

    if not session.get('user') or not session.get('is_admin'):
        print("Unauthorized access attempt")
        return jsonify({'status': 'error', 'message': 'Unauthorized'}), 403

    try:
        cur = mysql.connection.cursor()

        # Check if session exists before deleting
        cur.execute("SELECT COUNT(*) FROM user_sessions WHERE id = %s", (session_db_id,))
        session_exists = cur.fetchone()[0]
        print(f"Session exists: {session_exists}")

        if session_exists == 0:
            cur.close()
            return jsonify({'status': 'error', 'message': 'Session not found'}), 404

        # Delete the session
        cur.execute("DELETE FROM user_sessions WHERE id = %s", (session_db_id,))
        rows_affected = cur.rowcount
        mysql.connection.commit()
        cur.close()

        print(f"Rows affected: {rows_affected}")

        if rows_affected > 0:
            return jsonify({'status': 'success', 'message': 'User session terminated successfully'})
        else:
            return jsonify({'status': 'error', 'message': 'Session could not be terminated'}), 500

    except Exception as e:
        print(f"Error in admin_force_logout: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/admin/clear_session_data', methods=['POST'])
def admin_clear_session_data():
    """Admin route to clear all session data for debugging"""
    if not session.get('user') or not session.get('is_admin'):
        return jsonify({'status': 'error', 'message': 'Unauthorized'}), 403

    try:
        # Keep admin session info but clear everything else
        admin_user = session.get('user')
        admin_status = session.get('is_admin')
        admin_session_id = session.get('session_id')
        admin_user_id = session.get('user_id')

        # Clear all session data
        clear_all_session_data()

        # Restore admin session
        session['user'] = admin_user
        session['is_admin'] = admin_status
        session['session_id'] = admin_session_id
        session['user_id'] = admin_user_id

        return jsonify({'status': 'success', 'message': 'Session data cleared successfully'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/admin/clear_all_session_files', methods=['POST'])
def admin_clear_all_session_files():
    """Admin route to delete all session files (nuclear option)"""
    if not session.get('user') or not session.get('is_admin'):
        return jsonify({'status': 'error', 'message': 'Unauthorized'}), 403

    try:
        # Keep admin session info
        admin_user = session.get('user')
        admin_status = session.get('is_admin')
        admin_session_id = session.get('session_id')
        admin_user_id = session.get('user_id')

        # Clear all session files
        force_clear_all_session_files()

        # Clear and restore admin session
        clear_all_session_data()
        session['user'] = admin_user
        session['is_admin'] = admin_status
        session['session_id'] = admin_session_id
        session['user_id'] = admin_user_id

        return jsonify({'status': 'success', 'message': 'All session files deleted successfully'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/admin/test_json', methods=['POST'])
def test_json():
    """Test route to verify JSON responses work correctly"""
    if not session.get('user') or not session.get('is_admin'):
        return jsonify({'status': 'error', 'message': 'Unauthorized'}), 403

    return jsonify({'status': 'success', 'message': 'JSON response test successful'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
